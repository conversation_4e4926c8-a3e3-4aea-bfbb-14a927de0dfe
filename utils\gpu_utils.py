# -*- coding: utf-8 -*-
"""
GPU utility functions for the WOSS Seismic Analysis Tool.

This module provides utility functions for checking GPU availability
and handling GPU-related operations.
"""

import logging
import numpy as np

def check_gpu_availability():
    """
    Check if GPU is available and working.
    
    This function attempts to import cupy and perform a simple operation
    to verify that the GPU is available and working correctly.
    
    Returns:
        bool: True if GPU is available and working, False otherwise
    """
    try:
        import cupy as cp
        # Try to perform a simple operation to verify GPU is working
        a = cp.array([1, 2, 3])
        b = cp.sum(a)
        logging.info("GPU is available and working correctly.")
        return True
    except ImportError as e:
        logging.warning(f"GPU not available - CuPy import failed: {e}")
        return False
    except Exception as e:
        logging.warning(f"GPU available but not working correctly: {e}")
        return False

def get_gpu_info():
    """
    Get information about the available GPU(s).
    
    Returns:
        dict: Dictionary containing GPU information, or None if GPU is not available
    """
    try:
        import cupy as cp
        
        # Check if CUDA is available
        if not cp.cuda.is_available():
            logging.warning("CUDA is not available.")
            return None
        
        # Get device count
        device_count = cp.cuda.runtime.getDeviceCount()
        
        # Get information for each device
        devices = []
        for i in range(device_count):
            cp.cuda.runtime.setDevice(i)
            props = cp.cuda.runtime.getDeviceProperties(i)
            
            # Get memory info
            free, total = cp.cuda.runtime.memGetInfo()
            free_mb = free / (1024 ** 2)
            total_mb = total / (1024 ** 2)
            
            devices.append({
                'device_id': i,
                'name': props['name'].decode('utf-8'),
                'compute_capability': f"{props['major']}.{props['minor']}",
                'total_memory_mb': total_mb,
                'free_memory_mb': free_mb
            })
        
        return {
            'device_count': device_count,
            'devices': devices
        }
    except Exception as e:
        logging.warning(f"Failed to get GPU information: {e}")
        return None

def log_gpu_info():
    """
    Log information about the available GPU(s).
    """
    gpu_info = get_gpu_info()
    if gpu_info:
        logging.info(f"Found {gpu_info['device_count']} GPU device(s):")
        for i, device in enumerate(gpu_info['devices']):
            logging.info(f"  Device {i}: {device['name']} (Compute {device['compute_capability']})")
            logging.info(f"    Memory: {device['free_memory_mb']:.2f}MB free / {device['total_memory_mb']:.2f}MB total")
    else:
        logging.warning("No GPU information available.")

def process_traces_gpu(traces_array, dt, batch_size, descriptor_settings,
                      outputs_to_calculate, progress_callback=None):
    """
    Centralized GPU processing function for trace descriptors.

    Args:
        traces_array: 2D numpy array of traces (traces x samples)
        dt: Sampling interval
        batch_size: GPU batch size
        descriptor_settings: Dictionary of descriptor parameters
        outputs_to_calculate: List of outputs to calculate
        progress_callback: Optional callback for progress updates

    Returns:
        Dictionary of calculated descriptors
    """
    try:
        # Import GPU functions
        from utils.dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        from utils.processing import calculate_woss

        # Filter out params not used by GPU function
        filtered_params = {
            k: v for k, v in descriptor_settings.items()
            if k not in ['dt', 'epsilon', 'fdom_exponent', 'hfc_p95', 'hfc_percentile', 'spec_decrease_percentile', 'sample_percent', 'max_traces_for_stats', 'hfc_pc', 'spec_decrease_pc']
            and not k.endswith('_cmap_min') and not k.endswith('_cmap_max') and not k.endswith('_cmap_name')
            and not k.endswith('_min') and not k.endswith('_max') and not k.endswith('_colorscale')
        }

        # Ensure required components for WOSS
        if "WOSS" in outputs_to_calculate:
            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
            for comp in required_for_woss:
                if comp not in outputs_to_calculate:
                    outputs_to_calculate.append(comp)

        # Call appropriate GPU function
        if any(out in ['mag', 'mag_voice'] for out in outputs_to_calculate):
            # Use magnitude version if needed
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )
        else:
            # Use standard version
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )

        # Filter to requested outputs
        calculated_descriptors = {
            key: all_descriptors[key]
            for key in outputs_to_calculate
            if key in all_descriptors
        }

        # Calculate WOSS if requested
        if "WOSS" in outputs_to_calculate and all(
            comp in calculated_descriptors
            for comp in ["hfc", "norm_fdom", "mag_voice_slope"]
        ):
            # Create WOSS parameters from descriptor_settings
            woss_params = {
                'hfc_p95': descriptor_settings.get('hfc_p95', 1.0),
                'epsilon': descriptor_settings.get('epsilon', 1e-4),
                'fdom_exponent': descriptor_settings.get('fdom_exponent', 2.0)
            }

            # Calculate WOSS for each trace
            woss_array = np.zeros_like(calculated_descriptors["hfc"])
            for i in range(traces_array.shape[0]):
                trace_components = {
                    'hfc': calculated_descriptors["hfc"][i],
                    'norm_fdom': calculated_descriptors["norm_fdom"][i],
                    'mag_voice_slope': calculated_descriptors["mag_voice_slope"][i]
                }
                woss_array[i] = calculate_woss(trace_components, woss_params)

            calculated_descriptors["WOSS"] = woss_array

        return calculated_descriptors

    except Exception as e:
        logging.error(f"GPU processing error: {e}", exc_info=True)
        raise
