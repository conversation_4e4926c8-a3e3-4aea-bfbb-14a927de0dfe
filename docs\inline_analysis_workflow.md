# Inline/Crossline Analysis Workflow

## Overview

This document describes the end-to-end workflow for inline and crossline analysis in the WOSS Streamlit application, including the new export and visualization functionality in Step 5.

## Workflow Steps

### Step 1: Load Data
- Load seismic data and configure basic parameters
- Ensure data is properly loaded and indexed

### Step 2: Configure Display
- Set up colormap settings and display limits for spectral descriptors
- These settings will be used consistently throughout the analysis and visualization

### Step 3: Analyze Data - Inline/Crossline Analysis

#### Analysis Process
1. **Select Analysis Mode**: Choose "Single inline (all crosslines)" or "Single crossline (all inlines)"
2. **Select Line**: Choose the specific inline or crossline number to analyze
3. **Configure Parameters**: Set spectral analysis parameters (HFC, WOSS, etc.)
4. **Calculate Descriptors**: Run the GPU-based spectral analysis
5. **Export Results**: Export the calculated descriptors for Step 5 visualization

#### Data Structure
The analysis calculates spectral descriptors for all traces along the selected line:
- **Input**: Multiple traces along an inline or crossline
- **Output**: 2D arrays of spectral descriptors (traces × time_samples)
- **Descriptors**: HFC, WOSS, Spectral Decrease, Normalized Spectral Decrease, etc.

### Step 4: Export Results
The export functionality creates a comprehensive data package containing:
- Calculated spectral descriptors (2D arrays)
- Trace metadata and indices
- Processing parameters
- Timestamp and analysis mode information

### Step 5: View Analysis Results - Section Visualization

#### New Functionality
When inline/crossline analysis results are exported, Step 5 automatically switches to section visualization mode:

1. **Analysis Information Display**
   - Shows analysis mode (inline or crossline)
   - Displays the analyzed line number
   - Lists available spectral descriptors

2. **Section Visualization**
   - **X-axis**: Crossline numbers (for inline analysis) or Inline numbers (for crossline analysis)
   - **Y-axis**: Time (seconds)
   - **Color/Amplitude**: Spectral descriptor values
   - **Colormap**: Uses settings from Step 2 for consistency

3. **Interactive Features**
   - Descriptor selection dropdown
   - Section statistics display
   - Data shape and time range information

4. **Export Options**
   - Export section data to NPZ format
   - Navigate back to analysis
   - Start new analysis

## Data Flow

```
Step 3: Analyze Data
├── Load traces for selected inline/crossline
├── Calculate spectral descriptors using GPU
├── Store results in session state
└── Export results for Step 5

Step 5: View Results
├── Load exported analysis data
├── Validate data format (2D arrays)
├── Create section visualization
├── Apply colormap settings from Step 2
└── Display interactive section plots
```

## Technical Implementation

### Data Format
- **Descriptors**: 2D numpy arrays with shape (traces, time_samples)
- **Time Vector**: 1D array matching the time dimension
- **Trace Indices**: List of trace indices for position mapping
- **Metadata**: Processing parameters and analysis configuration

### Visualization
- Uses `plot_section_2d()` function from `utils/visualization.py`
- Automatically handles data orientation and interpolation
- Applies colormap limits from Step 2 configuration
- Supports both inline and crossline analysis modes

### Error Handling
- Validates data format and dimensions
- Provides informative error messages
- Logs detailed information for debugging
- Graceful fallback for missing data

## Usage Tips

1. **Colormap Configuration**: Set up colormap limits in Step 2 for optimal visualization
2. **Data Validation**: Ensure spectral analysis completes successfully before export
3. **Section Interpretation**: 
   - For inline analysis: X-axis shows crossline positions
   - For crossline analysis: X-axis shows inline positions
4. **Export Format**: NPZ files contain all necessary data for external analysis

## Troubleshooting

### Common Issues
1. **"No exported data found"**: Ensure analysis was completed and exported from Step 3
2. **"Data must be 2D"**: Check that spectral analysis produced valid 2D arrays
3. **"Shape mismatch"**: Verify time vector matches data dimensions

### Debug Information
The application logs detailed information about:
- Data shapes and orientations
- Colormap settings and limits
- Trace indexing and position mapping
- Visualization parameters

## Future Enhancements

Potential improvements to the workflow:
1. **Multi-line Analysis**: Support for analyzing multiple lines simultaneously
2. **3D Visualization**: Volume rendering of spectral attributes
3. **Attribute Comparison**: Side-by-side comparison of different descriptors
4. **Advanced Export**: Support for additional file formats (SEGY, CSV, etc.)
